defmodule RepobotWeb.Live.SourceFiles.IndexTest do
  use RepobotWeb.ConnCase, async: true
  use Repobot.Test.Fixtures

  import Mox
  import Phoenix.LiveViewTest
  import Repobot.Test.Fixtures

  setup :set_mox_from_context
  setup :verify_on_exit!

  setup do
    user = create_user(%{login: "test_user"})

    %{user: user}
  end

  describe "index" do
    setup %{user: user} do
      # Create source files with tags
      source_file1 =
        create_source_file(
          %{
            name: "mix.exs",
            content: "defmodule MyApp.MixProject do\n  use Mix.Project\nend",
            user_id: user.id
          },
          tags: ["elixir", "config"]
        )

      source_file2 =
        create_source_file(
          %{
            name: ".github/workflows/ci.yml",
            content: "name: CI\non: push",
            user_id: user.id
          },
          tags: ["config"]
        )

      # Create a repository and associate with source_file1
      repository =
        create_repository(%{
          name: "test-repo",
          owner: user.login,
          full_name: "#{user.login}/test-repo",
          organization_id: user.default_organization_id
        })

      # Update source_file1 with source repository
      {:ok, _} =
        Repobot.SourceFiles.update_source_file(source_file1, %{
          source_repository_id: repository.id
        })

      # Reload source_file1 with the updated association
      source_file1 = Repobot.SourceFiles.get_source_file!(source_file1.id)

      {:ok, %{source_files: [source_file1, source_file2], repository: repository}}
    end

    test "lists source files with their tags", %{conn: conn, user: user, repository: repository} do
      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Verify page title and description
      assert html =~ "Source Files"

      assert html =~
               "A list of all your source files that can be synced across repositories, grouped by their source repository"

      # Verify source files are listed with their tags
      assert has_element?(view, ~s|a:fl-contains("mix.exs")|)
      assert has_element?(view, ~s|tr:has(a:fl-contains("mix.exs")) span:fl-contains("elixir")|)
      assert has_element?(view, ~s|tr:has(a:fl-contains("mix.exs")) span:fl-contains("config")|)

      # Verify source repository is displayed in the header
      assert has_element?(view, ~s|h2 a:fl-contains("#{repository.full_name}")|)

      assert has_element?(
               view,
               ~s|tr:has(a:fl-contains(".github/workflows/ci.yml")) span:fl-contains("config")|
             )
    end

    test "can filter source files by tags", %{conn: conn, user: user} do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Initially shows all files
      assert has_element?(view, ~s|a:fl-contains("mix.exs")|)
      assert has_element?(view, ~s|a:fl-contains(".github/workflows/ci.yml")|)

      # Filter by elixir tag
      _rendered =
        view
        |> form("form[phx-change=filter_tags]", %{"tags[elixir]" => "elixir"})
        |> render_change()

      # Should only show mix.exs
      assert has_element?(view, ~s|a:fl-contains("mix.exs")|)
      refute has_element?(view, ~s|a:fl-contains(".github/workflows/ci.yml")|)
    end

    test "filter section only shows tags with source files", %{conn: conn, user: user} do
      # Create a tag that has no source files
      Repobot.Tags.create_tag(%{
        name: "unused-tag",
        user_id: user.id,
        organization_id: user.default_organization_id
      })

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Filter section should only show tags that have source files
      assert has_element?(view, ~s|label input[name="tags[elixir]"]|)
      assert has_element?(view, ~s|label input[name="tags[config]"]|)
      refute has_element?(view, ~s|label input[name="tags[unused-tag]"]|)
    end

    test "filter section only shows tags from current organization", %{conn: conn, user: user} do
      # Create another user with a different organization
      other_user = create_user(%{login: "other_user"})

      # Create a source file with tags for the other user in their organization
      create_source_file(
        %{
          name: "other_file.txt",
          user_id: other_user.id,
          organization_id: other_user.default_organization_id
        },
        tags: ["other-org-tag"]
      )

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Filter section should only show tags from current user's organization
      assert has_element?(view, ~s|label input[name="tags[elixir]"]|)
      assert has_element?(view, ~s|label input[name="tags[config]"]|)
      refute has_element?(view, ~s|label input[name="tags[other-org-tag]"]|)
    end

    test "can select multiple files", %{conn: conn, user: user} do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Initially no files are selected
      refute has_element?(view, "div", ~r/\d+ files selected/)

      # Get the first file's ID from any table
      file_id =
        view
        |> render()
        |> Floki.parse_fragment!()
        |> Floki.find(~s|input[phx-click="toggle_file_selection"]|)
        |> List.first()
        |> Floki.attribute("value")
        |> List.first()

      # Select first file using its specific ID
      _rendered =
        view
        |> element(~s|input[phx-click="toggle_file_selection"][phx-value-id="#{file_id}"]|)
        |> render_click()

      # Verify selection count is shown
      assert has_element?(view, "span", "1 files selected")
      assert has_element?(view, "button", "Manage Tags")

      # Clear selection
      _rendered =
        view
        |> element("button", "Clear Selection")
        |> render_click()

      # Verify selection is cleared
      refute has_element?(view, "div", ~r/\d+ files selected/)
    end

    test "can manage tags for multiple files", %{conn: conn, user: user} do
      # Create source files with different tags
      file1 =
        create_source_file(
          %{name: "file1.yml", user_id: user.id},
          tags: ["config"]
        )

      file2 =
        create_source_file(
          %{name: "file2.yml", user_id: user.id},
          tags: ["yaml"]
        )

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Select both files
      view
      |> element(~s|[data-test-select-file="#{file1.id}"]|)
      |> render_click()

      view
      |> element(~s|[data-test-select-file="#{file2.id}"]|)
      |> render_click()

      # Open the tag management modal
      view
      |> element("[data-test-manage-tags]")
      |> render_click()

      # The modal should show both tags as selected since each file has one of them
      assert has_element?(
               view,
               ~s|[data-test-tag-button="config"][data-test-tag-selected="true"]|
             )

      assert has_element?(view, ~s|[data-test-tag-button="yaml"][data-test-tag-selected="true"]|)

      # Save changes without modifying the selection
      view
      |> element("[data-test-save-tags]")
      |> render_click()

      # Verify both files now have both tags
      file1 = Repobot.SourceFiles.get_source_file!(file1.id)
      file2 = Repobot.SourceFiles.get_source_file!(file2.id)

      file1_tags = Enum.map(file1.tags, & &1.name) |> Enum.sort()
      file2_tags = Enum.map(file2.tags, & &1.name) |> Enum.sort()

      assert file1_tags == ["config", "yaml"]
      assert file2_tags == ["config", "yaml"]
    end

    test "can create and add new tags in bulk", %{conn: conn, user: user} do
      file1 = create_source_file(%{name: "file1.yml", user_id: user.id})
      file2 = create_source_file(%{name: "file2.yml", user_id: user.id})

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Select both files
      view
      |> element(~s|[data-test-select-file="#{file1.id}"]|)
      |> render_click()

      view
      |> element(~s|[data-test-select-file="#{file2.id}"]|)
      |> render_click()

      # Open the tag management modal
      view
      |> element("[data-test-manage-tags]")
      |> render_click()

      # Create a new tag
      view
      |> form("form[phx-submit=create_tag]", %{name: "new-tag"})
      |> render_submit()

      # Save changes
      view
      |> element("[data-test-save-tags]")
      |> render_click()

      # Verify both files have the new tag
      file1 = Repobot.SourceFiles.get_source_file!(file1.id)
      file2 = Repobot.SourceFiles.get_source_file!(file2.id)

      assert Enum.any?(file1.tags, &(&1.name == "new-tag"))
      assert Enum.any?(file2.tags, &(&1.name == "new-tag"))
    end

    test "can remove tags from multiple files", %{conn: conn, user: user} do
      # Create files with the same tags
      file1 =
        create_source_file(
          %{name: "file1.yml", user_id: user.id},
          tags: ["config", "yaml"]
        )

      file2 =
        create_source_file(
          %{name: "file2.yml", user_id: user.id},
          tags: ["config", "yaml"]
        )

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Select both files
      view
      |> element(~s|[data-test-select-file="#{file1.id}"]|)
      |> render_click()

      view
      |> element(~s|[data-test-select-file="#{file2.id}"]|)
      |> render_click()

      # Open the tag management modal
      view
      |> element("[data-test-manage-tags]")
      |> render_click()

      # Deselect the yaml tag
      view
      |> element(~s|[data-test-tag-button="yaml"]|)
      |> render_click()

      # Save changes
      view
      |> element("[data-test-save-tags]")
      |> render_click()

      # Verify both files now only have the config tag
      file1 = Repobot.SourceFiles.get_source_file!(file1.id)
      file2 = Repobot.SourceFiles.get_source_file!(file2.id)

      file1_tags = Enum.map(file1.tags, & &1.name)
      file2_tags = Enum.map(file2.tags, & &1.name)

      assert file1_tags == ["config"]
      assert file2_tags == ["config"]
    end

    test "shows bulk tag modal when clicking manage tags", %{conn: conn, user: user} do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Select the first file by finding the first checkbox
      file_id =
        view
        |> render()
        |> Floki.parse_fragment!()
        |> Floki.find(~s|input[phx-click="toggle_file_selection"]|)
        |> List.first()
        |> Floki.attribute("value")
        |> List.first()

      _rendered =
        view
        |> element(~s|input[phx-click="toggle_file_selection"][phx-value-id="#{file_id}"]|)
        |> render_click()

      # Open the modal
      _rendered =
        view
        |> element("button", "Manage Tags")
        |> render_click()

      # Verify modal is shown with tags
      assert has_element?(view, "h3", "Manage Tags")
      assert has_element?(view, ~s|button[phx-click="toggle_modal_tag"][phx-value-tag="elixir"]|)
      assert has_element?(view, ~s|button[phx-click="toggle_modal_tag"][phx-value-tag="config"]|)
    end

    test "bulk tag modal shows all tags including unused ones", %{conn: conn, user: user} do
      # Create a tag that has no source files
      {:ok, unused_tag} =
        Repobot.Tags.create_tag(%{
          name: "unused-tag",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Select a file
      file_id =
        view
        |> render()
        |> Floki.parse_fragment!()
        |> Floki.find(~s|input[phx-click="toggle_file_selection"]|)
        |> List.first()
        |> Floki.attribute("value")
        |> List.first()

      _rendered =
        view
        |> element(~s|input[phx-click="toggle_file_selection"][phx-value-id="#{file_id}"]|)
        |> render_click()

      # Open the modal
      _rendered =
        view
        |> element("button", "Manage Tags")
        |> render_click()

      # Verify modal shows all tags including the unused one
      assert has_element?(view, ~s|button[phx-click="toggle_modal_tag"][phx-value-tag="elixir"]|)
      assert has_element?(view, ~s|button[phx-click="toggle_modal_tag"][phx-value-tag="config"]|)

      assert has_element?(
               view,
               ~s|button[phx-click="toggle_modal_tag"][phx-value-tag="#{unused_tag.name}"]|
             )
    end

    test "bulk tag modal only shows tags from current organization", %{conn: conn, user: user} do
      # Create another user with a different organization
      other_user = create_user(%{login: "other_user"})

      # Create a tag for the other user in their organization
      {:ok, other_org_tag} =
        Repobot.Tags.create_tag(%{
          name: "other-org-tag",
          user_id: other_user.id,
          organization_id: other_user.default_organization_id
        })

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Select a file
      file_id =
        view
        |> render()
        |> Floki.parse_fragment!()
        |> Floki.find(~s|input[phx-click="toggle_file_selection"]|)
        |> List.first()
        |> Floki.attribute("value")
        |> List.first()

      _rendered =
        view
        |> element(~s|input[phx-click="toggle_file_selection"][phx-value-id="#{file_id}"]|)
        |> render_click()

      # Open the modal
      _rendered =
        view
        |> element("button", "Manage Tags")
        |> render_click()

      # Verify modal shows only tags from current user's organization
      assert has_element?(view, ~s|button[phx-click="toggle_modal_tag"][phx-value-tag="elixir"]|)
      assert has_element?(view, ~s|button[phx-click="toggle_modal_tag"][phx-value-tag="config"]|)

      refute has_element?(
               view,
               ~s|button[phx-click="toggle_modal_tag"][phx-value-tag="#{other_org_tag.name}"]|
             )
    end

    test "can delete a source file", %{conn: conn, user: user} do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Get the initial count of files
      initial_count = length(Repobot.SourceFiles.list_source_files(user))

      # Get the first file's ID
      file_id =
        view
        |> render()
        |> Floki.parse_fragment!()
        |> Floki.find(~s|input[phx-click="toggle_file_selection"]|)
        |> List.first()
        |> Floki.attribute("value")
        |> List.first()

      _rendered =
        view
        |> element(~s|button[phx-click="delete"][phx-value-id="#{file_id}"]|)
        |> render_click()

      # Verify file is removed from the list
      assert length(Repobot.SourceFiles.list_source_files(user)) == initial_count - 1
    end

    test "can bulk delete selected source files", %{conn: conn, user: user} do
      # Create additional source files for testing
      file1 = create_source_file(%{name: "file1.yml", user_id: user.id})
      file2 = create_source_file(%{name: "file2.yml", user_id: user.id})

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Get the initial count of files
      initial_count = length(Repobot.SourceFiles.list_source_files(user))

      # Select both files
      view
      |> element(~s|[data-test-select-file="#{file1.id}"]|)
      |> render_click()

      view
      |> element(~s|[data-test-select-file="#{file2.id}"]|)
      |> render_click()

      # Verify the bulk delete button is shown
      assert has_element?(view, "[data-test-bulk-delete]", "Delete Selected")

      # Click the bulk delete button
      view
      |> element("[data-test-bulk-delete]")
      |> render_click()

      # Verify files are removed from the list
      assert length(Repobot.SourceFiles.list_source_files(user)) == initial_count - 2

      # Verify selection is cleared after deletion
      refute has_element?(view, "div", ~r/\d+ files selected/)
    end

    test "bulk delete button shows correct confirmation message", %{conn: conn, user: user} do
      file1 = create_source_file(%{name: "file1.yml", user_id: user.id})
      file2 = create_source_file(%{name: "file2.yml", user_id: user.id})

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Select both files
      view
      |> element(~s|[data-test-select-file="#{file1.id}"]|)
      |> render_click()

      view
      |> element(~s|[data-test-select-file="#{file2.id}"]|)
      |> render_click()

      # Check that the bulk delete button has the correct confirmation message
      assert has_element?(
               view,
               ~s|button[data-confirm*="Are you sure you want to delete 2 selected source files"]|
             )
    end
  end

  describe "source files index with repository grouping" do
    test "lists files grouped by source repository", %{conn: conn, user: user} do
      # Create repositories
      repo1 =
        create_repository(%{
          name: "template-repo",
          owner: user.login,
          full_name: "#{user.login}/template-repo",
          template: true,
          organization_id: user.default_organization_id
        })

      repo2 =
        create_repository(%{
          name: "regular-repo",
          owner: user.login,
          full_name: "#{user.login}/regular-repo",
          template: false,
          organization_id: user.default_organization_id
        })

      # Create source files with different source repositories
      create_source_file(%{
        name: "template-file.md",
        user_id: user.id,
        source_repository_id: repo1.id
      })

      create_source_file(%{
        name: "another-template-file.yml",
        user_id: user.id,
        source_repository_id: repo1.id
      })

      create_source_file(%{
        name: "regular-file.txt",
        user_id: user.id,
        source_repository_id: repo2.id
      })

      create_source_file(%{
        name: "no-source-file.txt",
        user_id: user.id
      })

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Verify repository groups are shown
      assert has_element?(view, "h2 a", "#{user.login}/template-repo")
      assert has_element?(view, "h2 a", "#{user.login}/regular-repo")
      assert has_element?(view, "h2", "No source repository")

      # Verify template badge is shown for template repository
      assert has_element?(view, "span", "Template")

      # Verify files are grouped correctly under their repositories
      # Files from template-repo should be in the same section
      assert has_element?(
               view,
               "div:has(h2 a:fl-contains('template-repo')) a",
               "template-file.md"
             )

      assert has_element?(
               view,
               "div:has(h2 a:fl-contains('template-repo')) a",
               "another-template-file.yml"
             )

      # Files from regular-repo should be in their own section
      assert has_element?(view, "div:has(h2 a:fl-contains('regular-repo')) a", "regular-file.txt")

      # Files without source repository should be in "No source repository" section
      assert has_element?(
               view,
               "div:has(h2:fl-contains('No source repository')) a",
               "no-source-file.txt"
             )
    end

    test "shows category information in table even when grouped by repository", %{
      conn: conn,
      user: user
    } do
      # Create a category
      docs = create_category(user, %{name: "Documentation"})

      # Create a repository
      repo =
        create_repository(%{
          name: "test-repo",
          owner: user.login,
          full_name: "#{user.login}/test-repo",
          organization_id: user.default_organization_id
        })

      # Create source files with categories
      create_source_file(%{
        name: "README.md",
        user_id: user.id,
        source_repository_id: repo.id,
        category_id: docs.id
      })

      create_source_file(%{
        name: "uncategorized.txt",
        user_id: user.id,
        source_repository_id: repo.id
      })

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Verify category information is shown in the table
      assert has_element?(view, "tr:has(a:fl-contains('README.md')) td", "Documentation")
      assert has_element?(view, "tr:has(a:fl-contains('uncategorized.txt')) td", "Uncategorized")
    end

    test "handles files from other users correctly", %{conn: conn, user: user} do
      other_user = create_user()
      _organization = user.default_organization
      _other_organization = other_user.default_organization

      # Create categories and files for both organizations
      docs = create_category(user, %{name: "Documentation"})
      other_docs = create_category(other_user, %{name: "Documentation"})

      create_source_file(%{name: "README.md", user_id: user.id, category_id: docs.id})
      create_source_file(%{name: "OTHER.md", user_id: other_user.id, category_id: other_docs.id})

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Should only see our files
      assert has_element?(view, "a", "README.md")
      refute has_element?(view, "a", "OTHER.md")
    end
  end

  describe "auto-categorization" do
    test "can auto-categorize files using AI", %{conn: conn, user: user} do
      # Create some uncategorized files
      file1 = create_source_file(%{name: "README.md", user_id: user.id})
      file2 = create_source_file(%{name: ".github/workflows/ci.yml", user_id: user.id})

      # Mock the AI response
      Mox.expect(Repobot.Test.AIMock, :infer_categories, fn files, _organization ->
        assert length(files) == 2

        :timer.sleep(100)

        {:ok,
         %{
           file1.id => "Documentation",
           file2.id => "CI/CD"
         }}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      # Click the auto-categorize button
      render_click(view, "auto_categorize")

      # Verify that categories were created and files were assigned (shown in table cells)
      assert has_element?(view, "tr:has(a:fl-contains('README.md')) td", "Documentation")
      assert has_element?(view, "tr:has(a:fl-contains('.github/workflows/ci.yml')) td", "CI/CD")
    end

    test "handles AI categorization errors gracefully", %{conn: conn, user: user} do
      create_source_file(%{name: "README.md", user_id: user.id})

      Mox.expect(Repobot.Test.AIMock, :infer_categories, fn _files, _organization ->
        {:error, "API error"}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files")

      render_click(view, "auto_categorize")
    end
  end
end
